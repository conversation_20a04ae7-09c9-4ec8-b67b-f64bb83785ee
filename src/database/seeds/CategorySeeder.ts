import type { DataSource } from 'typeorm';
import type { Seeder, SeederFactoryManager } from 'typeorm-extension';

import { CategoryEntity } from '../../modules/category/category.entity.ts';

export default class CategorySeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    factoryManager: SeederFactoryManager,
  ): Promise<any> {
    console.info('🌱 Running CategorySeeder...');
    const categoryRepository = dataSource.getRepository(CategoryEntity);

    // Check if categories already exist
    const existingCategories = await categoryRepository.count();
    console.info(`Found ${existingCategories} existing categories`);

    if (existingCategories > 0) {
      console.info('Categories already exist, skipping seeding...');

      return;
    }

    const categories = [
      {
        name: 'Work',
        description: 'Tasks related to work and professional activities',
      },
      {
        name: 'Personal',
        description: 'Personal tasks and activities',
      },
      {
        name: 'Shopping',
        description: 'Shopping lists and purchase-related tasks',
      },
      {
        name: 'Health',
        description: 'Health and fitness related tasks',
      },
      {
        name: 'Education',
        description: 'Learning and educational tasks',
      },
      {
        name: 'Home',
        description: 'Household chores and home maintenance tasks',
      },
      {
        name: 'Finance',
        description: 'Financial planning and money-related tasks',
      },
      {
        name: 'Travel',
        description: 'Travel planning and trip-related tasks',
      },
    ];

    // Insert categories
    await categoryRepository.insert(categories);

    console.info(`Successfully seeded ${categories.length} categories`);
  }
}
