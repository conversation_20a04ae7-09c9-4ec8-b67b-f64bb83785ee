import './../../boilerplate.polyfill';

import dotenv from 'dotenv';

import { dataSource } from '../../../ormconfig.ts';
import CategorySeeder from './category-seeder.ts';

dotenv.config();

async function runSeeding() {
  try {
    console.info('🌱 Starting database seeding...');

    await dataSource.initialize();
    console.info('✅ Database connection established');

    // Run the category seeder directly
    const categorySeeder = new CategorySeeder();
    await categorySeeder.run(dataSource, {} as any);
    console.info('✅ Seeding completed successfully!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.info('🔌 Database connection closed');
    }
  }
}

await runSeeding().catch((error: unknown) => {
  console.error('Failed to run seeding:', error);
  process.exit(1);
});
